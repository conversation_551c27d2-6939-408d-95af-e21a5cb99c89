#!/usr/bin/env python3

import sys
import os
sys.path.append('/Users/<USER>/Downloads/text-recognize/venv/lib/python3.13/site-packages')

from paddleocr import PaddleOCR
import json

def test_paddleocr():
    print("Initializing PaddleOCR...")
    
    # Initialize PaddleOCR
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False
    )
    
    print("PaddleOCR initialized successfully")
    
    # Test with our image
    image_path = "/Users/<USER>/Downloads/text-recognize/test_image.png"
    
    print(f"Processing image: {image_path}")
    
    # Run OCR
    result = ocr.predict(image_path)
    
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result) if result else 'None'}")
    
    if result and len(result) > 0:
        first_result = result[0]
        print(f"First result type: {type(first_result)}")
        
        # Check attributes
        print(f"First result attributes: {dir(first_result)}")
        
        # Check if it has json attribute
        if hasattr(first_result, 'json'):
            print("Found json attribute!")
            json_data = first_result.json
            print(f"JSON data type: {type(json_data)}")
            print(f"JSON data keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'Not a dict'}")
            print(f"Full JSON data: {json_data}")
        else:
            print("No json attribute found")
            print(f"First result content: {first_result}")
            
            # Try to access as list
            if isinstance(first_result, list):
                print(f"First result is a list with {len(first_result)} items")
                for i, item in enumerate(first_result):
                    print(f"Item {i}: {item}")
    else:
        print("No results returned")

if __name__ == "__main__":
    test_paddleocr()
